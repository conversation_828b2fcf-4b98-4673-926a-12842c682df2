image: maven:3.8.7-eclipse-temurin-17  # Ensure apt-get, Maven, and Java 17 support

stages:
  - test
  - extract
  - notify

variables:
  MAVEN_OPTS: "-Dmaven.repo.local=.m2/repository"

test:
  stage: test
  allow_failure: true
  script:
    - mvn clean install -X
    - mvn $MAVEN_OPTS clean package
    - mvn compile test
  artifacts:
    when: always
    name: "report"
    paths:
      - target/surefire-reports/*
      - output/reports/*
      - target/surefire-reports/emailable-report.html
      - output/reports/ExtentReport.html
    expire_in: 10 h

extract_test_results:
  stage: extract
  image: python:3.9  # Use Python for parsing the HTML report
  script:
    - pip install beautifulsoup4
    - echo "Extracting test results from ExtentReport.html..."
    - |
      python - <<EOF
      from bs4 import BeautifulSoup
      import re

      file_path = "output/reports/ExtentReport.html"

      try:
          with open(file_path, "r", encoding="utf-8") as file:
              soup = BeautifulSoup(file, "html.parser")

          test_results = []
          test_items = soup.find_all("li", class_="test-item")

          for test in test_items:
              test_name = test.find("p", class_="name").text.strip()
              status = test.find("span", class_="badge").text.strip()
              order_id = "N/A"

              details_section = test.find_next("div", class_="test-contents")
              if details_section:
                  order_rows = details_section.find_all("tr", class_="event-row")
                  for row in order_rows:
                      if "dealskart_order_id=" in row.text:  # Find specific order ID
                          match = re.search(r"dealskart_order_id=(\d+)", row.text)
                          if match:
                              order_id = match.group(1)  # Extract the number
                          break  # Stop after finding first occurrence

              test_results.append(f"<tr><td>{test_name}</td><td>{order_id}</td><td>{status}</td></tr>")

          email_body = f'''
          <html>
          <head><style>
            table {{border-collapse: collapse; width: 100%;}}
            th, td {{border: 1px solid black; padding: 8px; text-align: left;}}
            th {{background-color: #f2f2f2;}}
          </style></head>
          <body>
          <h2>Test Execution Report for POS</h2>
          <table>
            <tr><th>Test Case Name</th><th>Order ID</th><th>Status</th></tr>
            {"".join(test_results)}
          </table>
          </body>
          </html>
          '''

          with open("extracted_email_body.html", "w", encoding="utf-8") as output_file:
              output_file.write(email_body)

          print("Test results extracted successfully.")

      except Exception as e:
          print(f"Error extracting test results: {e}")
          exit(1)
      EOF
  artifacts:
    when: always
    name: "parsed-test-results"
    paths:
      - extracted_email_body.html  # Ensure this file is available for send_email stage
    expire_in: 10h

send_email:
  stage: notify
  script:
    - echo "Updating package list & installing jq and curl..."
    - apt-get update && apt-get install -y jq curl  # Ensure required tools are installed

    - echo "Checking if extracted_email_body.html exists..."
    - ls -l extracted_email_body.html  # Debugging step to verify the file is present

    - echo "Encoding ExtentReport.html to Base64..."
    - BASE64_ENCODED_CONTENT=$(base64 -w 0 output/reports/ExtentReport.html | tr -d '\n')

    - echo "Formatting email content..."
    - EMAIL_BODY=$(jq -Rs '.' extracted_email_body.html)  # Read & escape JSON properly

    - echo "Sending email via SendGrid..."
    - |
      curl --location 'https://api.sendgrid.com/v3/mail/send' \
        --header 'Authorization: Bearer *********************************************************************' \
        --header 'Content-Type: application/json' \
        --data-raw "{
          \"personalizations\": [{
            \"to\": [
              {\"email\": \"<EMAIL>\"},
              {\"email\": \"<EMAIL>\"}
              
            ]
          }],
          \"from\": {\"email\": \"<EMAIL>\"},
          \"subject\": \"POS Test Report - GitLab Pipeline\",
          \"content\": [{\"type\": \"text/html\", \"value\": $EMAIL_BODY}],
          \"attachments\": [{
            \"content\": \"${BASE64_ENCODED_CONTENT}\",
            \"type\": \"text/html\",
            \"filename\": \"ExtentReport.html\"
          }]
        }"