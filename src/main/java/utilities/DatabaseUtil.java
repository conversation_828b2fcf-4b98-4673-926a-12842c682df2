package utilities;

import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import constants.FilesPath;
import enums.PropertyKeys;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import utilities.lenskart.ExcelSheetUtil;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;

public final class DatabaseUtil {
    private static final Logger logger = LogManager.getLogger(DatabaseUtil.class);
    private static String mySqlHost = "127.0.0.1";
    private static int mysqlHostPort = 3333;
    private static Session session = null;
    private static Map<String, String> dbProperty = null;
    private static Connection mysqlConnection = null;

    private DatabaseUtil() {}

    public static synchronized Connection getMysqlConnection() {
        logger.debug("Fetching MySQL connection");

        if (Objects.isNull(mysqlConnection) || !isConnectionValid()) {
            boolean isLocal = isRunningOnLocalMachine();
            try {
                if (isLocal) {
                    initSSHConnection();
                } else {
                    initDBConnectionWithoutSSH();
                }
            } catch (Exception e) {
                throw new RuntimeException("Failed to establish MySQL connection", e);
            }
        }
        return mysqlConnection;
    }

    private static boolean isRunningOnLocalMachine() {
        String osName = System.getProperty("os.name").toLowerCase();
        return osName.contains("mac") || osName.contains("win");
    }

    private static synchronized boolean isConnectionValid() {
        final String CHECK_SQL_QUERY = "SELECT 1";
        boolean isConnected = false;
        try {
            mysqlConnection.prepareStatement(CHECK_SQL_QUERY).execute();
            isConnected = true;
        } catch (SQLException | NullPointerException e) {
            logger.error("Database connection lost. Reconnecting...");
        }
        return isConnected;
    }

    private static synchronized void createMySQLConnection(Map<String, String> dbProperty) throws SQLException {
        String url = "jdbc:mysql://" + mySqlHost + ":" + mysqlHostPort + "/" + dbProperty.get("DB_NAME");

        Properties properties = new Properties();
        properties.setProperty("user", dbProperty.get("DB_USER_ID"));
        properties.setProperty("password", dbProperty.get("DB_PASSWORD"));
        properties.setProperty("useSSL", "false");
        properties.setProperty("autoReconnect", "true");

        mysqlConnection = DriverManager.getConnection(url, properties);
        logger.debug("Successfully created MySQL connection");
    }

    private static synchronized void createMySQLConnectionWithHardcodedPwd(Map<String, String> dbProperty) throws SQLException {
        String url = "jdbc:mysql://" + mySqlHost + ":" + mysqlHostPort + "/" + dbProperty.get("DB_NAME");

        Properties properties = new Properties();
        properties.setProperty("user", "manjitp");
        properties.setProperty("password", "SpZy&4>5x%3q");
        properties.setProperty("useSSL", "false");
        properties.setProperty("autoReconnect", "true");

        mysqlConnection = DriverManager.getConnection(url, properties);
        logger.debug("Successfully created MySQL connection with hardcoded password");
    }

    private static void initDBConnectionWithoutSSH() throws SQLException {
        if (Objects.isNull(dbProperty)) {
            synchronized (DatabaseUtil.class) {
                if (Objects.isNull(dbProperty)) {
                    dbProperty = fetchDBPropertiesFromExcel();
                }
            }
        }

        if (Objects.isNull(mysqlConnection) || !isConnectionValid()) {
            logger.debug("Initializing direct database connection");
            mySqlHost = "mariadb-read-all.prod.internal";
            mysqlHostPort = 3306;
            createMySQLConnectionWithHardcodedPwd(dbProperty);
        }
    }

    private static void initSSHConnection() throws SQLException {
        if (Objects.isNull(dbProperty)) {
            synchronized (DatabaseUtil.class) {
                if (Objects.isNull(dbProperty)) {
                    dbProperty = fetchDBPropertiesFromExcel();
                }
            }
        }

        if (Objects.isNull(session) || !session.isConnected()) {
            logger.debug("Initiating SSH connection");
            String sshHost = dbProperty.get("SSH_Host");

            if (Objects.nonNull(sshHost) && !sshHost.isEmpty()) {
                try {
                    JSch jsch = new JSch();
                    session = jsch.getSession(dbProperty.get("SSH_UserName"), sshHost, 22);
                    session.setConfig("StrictHostKeyChecking", "No");
                    session.setPassword(dbProperty.get("SSH_Password"));
                    session.connect(10000);
                    session.setPortForwardingL(mysqlHostPort, dbProperty.get("DB_IP"), 3306);
                    logger.debug("SSH connection successful: {}", session.isConnected());
                } catch (JSchException e) {
                    throw new RuntimeException("SSH Connection Failed", e);
                }
            }
        }
        createMySQLConnection(dbProperty);
    }

    private static Map<String, String> fetchDBPropertiesFromExcel() {
        logger.debug("Fetching DB properties from Excel");
        List<Map<String, String>> excelData = ExcelSheetUtil.readExcelFile(FilesPath.getTestManagerExcelFile(), "DBProperty");
        String environment = PropertyUtil.getPropertyValue(PropertyKeys.environment);

        return excelData.stream()
                .filter(row -> environment.equalsIgnoreCase(row.get("Environment")))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("DB properties not found for environment: " + environment));
    }

    public static synchronized void closeMysqlConnection() {
        try {
            if (Objects.nonNull(mysqlConnection)) {
                mysqlConnection.close();
                mysqlConnection = null;
                logger.info("Closed MySQL connection");
            }
        } catch (SQLException e) {
            throw new RuntimeException("Error while closing MySQL connection", e);
        }

        if (Objects.nonNull(session) && session.isConnected()) {
            session.disconnect();
            logger.info("Closed SSH session");
        }
    }
}
