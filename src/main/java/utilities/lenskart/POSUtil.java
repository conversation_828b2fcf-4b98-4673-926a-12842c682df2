package utilities.lenskart;

import constants.Queries;
import constants.StoreCredential;
import enums.Franchisies;
import enums.PrescriptionTypes;
import enums.ProductClassification;
import exceptions.runTimeException.FrameWorkRunTimeException;
import io.restassured.response.Response;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;
import pojo.junoservice.websearch.ProductList;
import pojo.junoservice.websearch.WebSearch;
import pojo.junoservice.websearch.authenticationController.auth.AuthResponse;
import pojo.junoservice.websearch.authenticationController.auth.JsonAuthResponse;
import pojo.orderservice.cart.CartResponse;
import pojo.orderservice.cart.CartResult;
import pojo.orderservice.cart.Discount;
import pojo.orderservice.cart.Item;
import pojo.orderservice.cart.productcontroller.*;
import services.juno.Product;
import services.orderservice.CartController;
import services.orderservice.ProductController;
import services.webservice.AuthenticationController;
import utilities.MySQLUtil;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

public final class POSUtil {
    private static final Logger logger = LogManager.getLogger(POSUtil.class);
    /*
    public String getPOSSession(Franchisies franchise) {
        Response auth = AuthenticationController.auth( StoreCredential.getUsername( franchise ), StoreCredential.getPassword( franchise ) );
        auth.then().statusCode( 200 );
        System.out.println( "Session = " + auth.jsonPath().getString( "sessionToken" ) );
        return auth.jsonPath().getString( "sessionToken" );
    }

     */

    public AuthResponse getAuth(Franchisies franchise) {
        Response auth = AuthenticationController.auth( StoreCredential.getUsername( franchise ), StoreCredential.getPassword( franchise ) );
        auth.then().statusCode( 200 );
        return auth.as( AuthResponse.class );
    }

    public JsonAuthResponse getJunoSessionToken(){
        Response junoToken=AuthenticationController.junoSessionToken();
        junoToken.then().statusCode(200);
        return junoToken.as(JsonAuthResponse.class);
    }

    public CartResult removeAutoAddedItemFromCart(@NotNull CartResult cartResponse, @NotNull String posSession) {
        while(isAutoAddedItemPresent(cartResponse)) {
            for (Item item : cartResponse.items) {
                logger.info( item.productId + ", AutoAdded " + item.isAutoAdded + ", ModelName: " + item.modelName + ", item_id : "+ item.id);
                if ((item.isAutoAdded || (item.modelName != null && "Insurance VI".equals(item.modelName))) && cartResponse.items.size()>1) {
                    Response cart2 = CartController.removeItem( posSession, cartResponse.junoSessionToken, String.valueOf( item.id ) );
                    cart2.then().statusCode( 200 );
                    cartResponse = cart2.as( CartResult.class );
                    System.out.println(cartResponse.itemsQty);
                    logger.debug( "Cart version " + cartResponse.cartVersion );
                   Response updatedCart= CartController.getCart(posSession,cartResponse.junoSessionToken);
                   cartResponse=updatedCart.as(CartResult.class);
                }
            }
        }
        return cartResponse;
    }

    public boolean removeAutoAddedDiscountIfPresent(String posSession, String junoSession, @NotNull CartResult cartResponse){
        if(!cartResponse.totals.discounts.isEmpty()){
            logger.info("auto Discount applicable need to remove");
            isAutoAddedVoucherRemoved(posSession,junoSession,cartResponse);
            return true;
        }

        logger.info("auto Discount not applicable ");
        return false;
        //Discount discount= cartResponse.totals.discounts.get(0);

    }
    public boolean isAutoAddedVoucherRemoved(String posSession, String junoSession, @NotNull CartResult cartResponse){
        Discount discount= cartResponse.totals.discounts.get(0);
        logger.info("Discount code "+discount.code);

        if(!discount.code.isEmpty()){
            CartController.removeVoucherFromCart(posSession,junoSession,discount.code);
            logger.info("Discount code removed");
            return true;
        }
        return false;
    }
    public boolean isAutoAddedItemPresent(CartResult cartResponse){
        for(Item item : cartResponse.items){
            if(item.isAutoAdded){
                logger.info("Cart has AutoAdded item");
                return true;
            }
        }
        return false;
    }

    public boolean isInsuranceVIItemPresent(CartResult cartResponse){
        for(Item item : cartResponse.items){
            if(item.modelName != null && "Insurance VI".equals(item.modelName)){
                logger.info("Cart has Insurance VI item");
                return true;
            }
        }
        return false;
    }

    public String fetchPackageId(String posSession, PrescriptionTypes prescriptionType, String productId,String junoSessionToken) {
        Response getPackagesResponse = ProductController.getPackages( posSession, prescriptionType, productId ,junoSessionToken);
        getPackagesResponse.then().statusCode( 200 );
//        Map<String, Object> packages = getPackagesResponse.getBody().jsonPath().getMap("result.packages[0].id");
//        System.out.println("package id is "+packages.get("id"));
        String packageId = getPackagesResponse.getBody().jsonPath().getString( "result.packages[0].id" );
        logger.debug( "package id is " + packageId );
        return packageId;
    }

    /**
     * This method uses JUNO API for search, checks whether PID has qty>0 and supports prescriptionType. It also fetches packages
     * supported for given prescriptionType. Returns a Map<String, String> containing productId, packageId, frameType and prescriptionType
     * @param posSession
     * @param searchKey
     * @param prescriptionType
     * @return productInfo
     */
    public Map<String, String> getProductPackageId(String posSession, String searchKey, PrescriptionTypes prescriptionType,String junoToken) {
        String productId = null;
        ProductResult productResponse = null;

        // Websearch
        Response apiResponse = Product.webSearch( searchKey );
        apiResponse.then().statusCode( 200 );
        WebSearch webSearch = apiResponse.as( WebSearch.class );

        // iterating over all product if product has qty and supports prescriptionType
        for (ProductList product : webSearch.result.product_list) {
            System.out.println( "Product id " + product.id );
            if (product.qty > 0) {
                apiResponse = ProductController.getProduct( posSession, product.id ,junoToken);
                System.out.println(apiResponse.asPrettyString());
                apiResponse.then().statusCode( 200 );
                productResponse = apiResponse.as( ProductResult.class );

                // checking whether PID supports prescriptionType
                for (ProductPrescriptionTypes prescription : productResponse.prescriptionType) {
                    if (prescriptionType.name().equalsIgnoreCase( prescription.id )) {
                        productId = productResponse.id;
                        break;
                    }
                }
                if (Objects.nonNull( productId ))
                    break;
            }
        }

        // if PID do not support prescription type stopping execution
        if (Objects.isNull( productId ))
            throw new FrameWorkRunTimeException( "Could not find " + prescriptionType.name() + " for " + searchKey );

        // fetching frametype
        String frameType = null;
        for (ProductSpecification specification : productResponse.specifications) {
            if (specification.name.equalsIgnoreCase( "technical" )) {
                for (ProductItems item : specification.items) {
                    if (item.name.equalsIgnoreCase( "Frame Type" ))
                        frameType = item.value;
                }

            }
        }

        // fetching package id based on frame Type
        Response packagesResponse = ProductController.getPackages( posSession, prescriptionType, productResponse.id, frameType,junoToken );
        System.out.println( packagesResponse.asString() );
        packagesResponse.then().statusCode( 200 );
        String packageId = packagesResponse.jsonPath().getString( "packages[0].id" );
        System.out.println( "productId " + productId + ", pckageId=" + packageId + ", frametype " + frameType );

        Map<String, String> productInfo = new HashMap<>();
        productInfo.put( "productId", productId );
        productInfo.put( "packageId", packageId );
        productInfo.put( "frameType", frameType );
        productInfo.put( "prescriptionType", prescriptionType.toString() );
        return productInfo;
    }

    public Set<String> getPIDsAvailableInStore(AuthResponse authResponse, ProductClassification classification){
        Set<String> productIds = new HashSet<>();
        ResultSet resultSet = null;
        if(authResponse.franchiseType == 1)
            resultSet = MySQLUtil.executeSelectQuery( Queries.getFranchiseStoreQtyProductIds( authResponse.facilityCode, classification ) );
        else
            resultSet = MySQLUtil.executeSelectQuery( Queries.getLenskartStoreQtyProductIds( authResponse.facilityCode, classification ) );
        try{
            while (resultSet.next()){
                String productId = resultSet.getString( "skuCode" );
                System.out.print(productId + ", ");
                productIds.add( productId );
            }
        } catch (SQLException e) {
            throw new RuntimeException( "Unable to get skuCode from query result", e );

        }
        return productIds;
        }

        public Map<String, List<String>> fetchBarcodeAvailableInStore(AuthResponse authResponse, ProductClassification classification) {
            Map<String, List<String>> pids = new HashMap<>();
            String barcode = null;
            logger.info( "Fetching PIDs available in store from DB" );
            Set<String> piDsAvailableInStore = getPIDsAvailableInStore( authResponse, classification );
            logger.info( "Fetching barcodes of {} ", piDsAvailableInStore );
            for (String pid : piDsAvailableInStore) {
                Response barcodesResponse = services.webservice.ProductController.getBarcodes( pid, authResponse.sessionToken );
                if(barcodesResponse.statusCode() != 200)
                    throw  new FrameWorkRunTimeException( "/v1/products/{id}/barcodes is not giving 200 response" );
            try{
                if(barcodesResponse.jsonPath().getList( pid ).size()>0){
                    pids.put( pid,  barcodesResponse.jsonPath().getList( pid ));
                }
            }catch (NullPointerException e){
                try{
                    if(barcodesResponse.jsonPath().getList( "barcodes" ).size()>0){
                        pids.put( pid,  barcodesResponse.jsonPath().getList( "barcodes" ));
                    }
                }catch (NullPointerException f){
                    logger.error( "Unable to fetch barcode for {}", pid );
                }
            }
            }
            return pids;
        }

        public List<String> fetchBarcode(AuthResponse authResponse, String productId){
            List<String> barcodes = new ArrayList<>();
            Response barcodesResponse = services.webservice.ProductController.getBarcodes( productId, authResponse.sessionToken );
            if(barcodesResponse.statusCode() != 200)
                throw  new FrameWorkRunTimeException( "/v1/products/{id}/barcodes is not giving 200 response" );
            try{
                if(barcodesResponse.jsonPath().getList( productId ).size()>0){
                    barcodes = barcodesResponse.jsonPath().getList( productId );
                }
            }catch (NullPointerException e){
                try{
                    if(barcodesResponse.jsonPath().getList( "barcodes" ).size()>0){
                        barcodes = barcodesResponse.jsonPath().getList( "barcodes" );
                    }
                }catch (NullPointerException f){
                    logger.error( "Unable to fetch barcode for {}", productId );
                }
            }
            return barcodes;
        }


        public Map<String, String> getLocalFittingEligiblePIDPackage(int franchise_id, int localfitting){
        Map<String, String> PIDdetails = new HashMap<>();
        ResultSet resultSet = MySQLUtil.executeSelectQuery( Queries.getLocalFittingEligiblePID( franchise_id, localfitting ));
        System.out.println("successfully executed query");
        try{
            while (resultSet.next()){
                PIDdetails.put( "product_id", resultSet.getString( "product_id" ) );
                PIDdetails.put( "selectedPackageID", resultSet.getString( "selectedPackageID" ) );
                PIDdetails.put( "local_fitting", resultSet.getString( "local_fitting" ) );
                PIDdetails.put( "fitting_facility", resultSet.getString( "fitting_facility" ) );
            }
        } catch (Exception e) {
            throw new RuntimeException( "Unable to get LocalFittingEligiblePIDPackage for franchise_id:"+franchise_id+", localfitting:"+localfitting,e );
        }
            PIDdetails.entrySet().forEach( entry -> {
                System.out.println(entry.getKey()+" : "+entry.getValue()+", ");
                if(Objects.isNull( entry.getValue() ))
                    throw new RuntimeException("necessary values are not present to place localfitting order");
            } );
        return PIDdetails;
        }

        public Map<String,String> getKafkaStatus(String dealsKartId){
            Map<String,String > kafkaStatus=new HashMap<>();
            ResultSet resultSet = MySQLUtil.executeSelectQuery(Queries.getKafkaValidationDBQuery(dealsKartId));
            try {
                while (resultSet.next()) {
                    kafkaStatus.put("commisson_inventory_status",String.valueOf(resultSet.getString("commission_inventory_status")));
                    kafkaStatus.put("commisson_pos_status",String.valueOf(resultSet.getString("commission_pos_status")));
                    kafkaStatus.put("shipping_margin_status",String.valueOf(resultSet.getString("shipping_margin_status")));
                    kafkaStatus.put("credit_margin_status",String.valueOf(resultSet.getString("credit_margin_status")));

                }
            }
            catch (Exception e){
                throw new RuntimeException("kafka status is not fetching");
            }

            return kafkaStatus;
        }



}
