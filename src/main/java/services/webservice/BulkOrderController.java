
package services.webservice;

import enums.PropertyKeys;
import utilities.MySQLUtil;
import utilities.PropertyUtil;
import utilities.lenskart.ExcelSheetUtil;
import utilities.lenskart.POSUtil;
import constants.APIConstants;
import constants.BaseURI;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import services.APIBase;

import java.io.File;
import java.util.List;
import java.util.Map;

import static io.restassured.RestAssured.given;

public class BulkOrderController {
    static POSUtil posUtil = new POSUtil();



    //India Orders (Preprod)
    List<Map<String, String>> india_data_preprod = List.of(
            Map.of("facilityCode", "LKS18", "itemSku", "135367", "quantity", "1")
    );

    //India Orders (Prod)
    List<Map<String, String>> india_data_prod = List.of(
            Map.of("facilityCode", "LKST4000", "itemSku", "151705", "quantity", "1")
    );

    // International Orders (Preprod)
    List<Map<String, String>> international_data_preprod = List.of(
            Map.of("facilityCode", "SGD1", "itemSku", "149089", "quantity", "1")

    );

    // International Orders (Prod)
    List<Map<String, String>> international_data_prod = List.of(
            Map.of("facilityCode", "LKST375", "itemSku", "131315", "quantity", "1")

    );


    String environment = PropertyUtil.getPropertyValue(PropertyKeys.environment); // "prod" or "preprod"
//    String orderType = PropertyUtil.getPropertyValue(PropertyKeys.orderType); // "india" or "international"

    List<Map<String, String>> data;

    public String generateSCVForBulkOrder(String country) {
        // ✅ Select Correct Dataset Based on `environment` & `orderType`
        if ("International".equalsIgnoreCase(country)) {
            data = "prod".equalsIgnoreCase(environment) ? international_data_prod : international_data_preprod;
        } else { // Default to India orders
            data = "prod".equalsIgnoreCase(environment) ? india_data_prod : india_data_preprod;
        }

        // Generate CSV and return query for validation
        ExcelSheetUtil.generateCSV(data);
        return MySQLUtil.buildValidationQueryForBulkOrder(data);
    }

    public Response postBulkFileUpload(String posSession) {
        File file = new File("generated_files/bulk_order.csv");

        if (!file.exists()) {
            throw new RuntimeException("CSV file not found at: " + file.getAbsolutePath());
        }

        RequestSpecification requestSpecification = given()
                .header("Accept", "application/*")
                .header("X-Api-Client", "pos_web")
                .headers(APIConstants.headersIdKeyOnly)
                .header("X-Lenskart-Session-Token", posSession)
                .header("Cookie", "__cf_bm=x3SzBqYs_RYdR2_UGiGzxVEgMkIXurhsbjly41.u1JE-1738930593-*******-IIOTxPgHoM5Xg8JhjMbGr9k1yQJlUk8kuCxqwAhVf7h9tcllyYoNjnmLJWdsM0t0Q2musr93GkAtETlJSMjrFQ")
                .contentType("multipart/form-data")
                .multiPart("file", file, "text/csv")
                .baseUri(BaseURI.getInstance().webService)
                .basePath("/v1/bulk");

        return requestSpecification.post();
    }
}